# Tips to build inventory https://docs.ansible.com/ansible/latest/getting_started/get_started_inventory.html#tips-for-building-inventories
# Ensure that group names are meaningful and unique. Group names are also case sensitive.
# Avoid spaces, hyphens, and preceding numbers (use floor_19, not 19th_floor) in group names.
# Group hosts in your inventory logically according to their What, Where, and When.
# What
#   Group hosts according to the topology, for example: db, web, leaf, spine.
# Where
#   Group hosts by geographic location, for example: datacenter, region, floor, building.
# When
#   Group hosts by stage, for example: development, test, staging, production.
bots:
  hosts:
    hlbb1:
      ansible_host: **************
    hlbb2:
      ansible_host: *************
    hlbg1:
      ansible_host: ************
    hlbn1:
      ansible_host: ***********
    hlbg2:
      ansible_host: ***********
    hyper-gate-1:
      ansible_host: *************
    hyper-binance-1:
      ansible_host: **************
  vars:
    ansible_user: ec2-user

ungrouped:
  hosts:
    arbcli:
      ansible_host: *************
      ansible_user: root
