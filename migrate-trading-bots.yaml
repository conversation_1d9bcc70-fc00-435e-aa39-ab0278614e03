---
- name: Migrate Trading Bots Between Machines
  hosts: localhost
  gather_facts: yes
  vars_prompt:
    - name: source_host
      prompt: "Enter the source host (hostname or IP) to migrate FROM"
      private: false
    - name: destination_host
      prompt: "Enter the destination host (hostname or IP) to migrate TO"
      private: false
    - name: confirm_migration
      prompt: "Are you sure you want to migrate? (yes/no)"
      private: false
      default: "no"

  vars:
    ansible_user: ec2-user
    migration_temp_dir: "/tmp/bot-migration-{{ ansible_date_time.epoch }}"
    files_to_migrate:
      - src: "/home/<USER>/.env"
        dest: "/home/<USER>/.env"
        backup: true
        encrypt: true
      - src: "/home/<USER>/redis-data/dump.rdb"
        dest: "/home/<USER>/redis-data/dump.rdb"
        backup: true
        encrypt: false

  pre_tasks:
    - name: Validate migration confirmation
      ansible.builtin.fail:
        msg: "Migration cancelled by user"
      when: confirm_migration != "yes"

    - name: Validate source and destination hosts are different
      ansible.builtin.fail:
        msg: "Source and destination hosts cannot be the same"
      when: source_host == destination_host

    - name: Generate encryption key once for the entire migration
      ansible.builtin.set_fact:
        migration_encryption_key: "{{ lookup('password', '/dev/null chars=ascii_letters,digits length=32') }}"
      run_once: true

    - name: Display encryption key for debugging (first 8 chars only)
      ansible.builtin.debug:
        msg: "Using encryption key starting with: {{ migration_encryption_key[:8] }}..."
      run_once: true

    - name: Create temporary migration directory
      ansible.builtin.file:
        path: "{{ migration_temp_dir }}"
        state: directory
        mode: '0755'
      delegate_to: localhost
      run_once: true

    - name: Prefetch Docker images on destination server
      block:
        - name: Check if destination machine is accessible
          ansible.builtin.ping:
          delegate_to: "{{ destination_host }}"

        - name: Check BOT_VERSION from source machine .env file
          ansible.builtin.shell:
            cmd: grep "^BOT_VERSION=" /home/<USER>/.env | cut -d'=' -f2 | tr -d '"' || echo "latest"
          delegate_to: "{{ source_host }}"
          register: bot_version_check
          failed_when: false

        - name: Set bot version variable
          ansible.builtin.set_fact:
            bot_version: "{{ bot_version_check.stdout | default('latest') }}"

        - name: Display bot version to be pulled
          ansible.builtin.debug:
            msg: "Will pull Docker image: arbteam/arbitrage:{{ bot_version }}"

        - name: Pull arbitrage Docker image on destination (specific version)
          ansible.builtin.command:
            cmd: docker pull arbteam/arbitrage:{{ bot_version }}
          delegate_to: "{{ destination_host }}"
          register: arbitrage_pull_result
          failed_when: false

        - name: Pull latest arbitrage Docker image on destination (fallback)
          ansible.builtin.command:
            cmd: docker pull arbteam/arbitrage:latest
          delegate_to: "{{ destination_host }}"
          register: arbitrage_latest_pull_result
          failed_when: false
          when: arbitrage_pull_result.rc != 0

        - name: Pull Redis Alpine Docker image on destination
          ansible.builtin.command:
            cmd: docker pull redis:alpine
          delegate_to: "{{ destination_host }}"
          register: redis_pull_result
          failed_when: false

        - name: Display Docker image pull results
          ansible.builtin.debug:
            msg: |
              Docker image prefetch results:
              - arbitrage:{{ bot_version }}: {{ 'Success' if arbitrage_pull_result.rc == 0 else 'Failed' }}
              - arbitrage:latest (fallback): {{ 'Success' if arbitrage_latest_pull_result is defined and arbitrage_latest_pull_result.rc == 0 else 'Skipped' if arbitrage_pull_result.rc == 0 else 'Failed' }}
              - redis:alpine: {{ 'Success' if redis_pull_result.rc == 0 else 'Failed - ' ~ redis_pull_result.stderr | default('Unknown error') }}

        - name: Warn if Docker image pulls failed
          ansible.builtin.debug:
            msg: |
              WARNING: Some Docker images failed to pull. This may cause delays during bot startup.
              - Check Docker daemon status on destination server
              - Verify internet connectivity on destination server
              - Images will be pulled automatically when bot starts, but this may take time

              Failed pulls:
              {% if arbitrage_pull_result.rc != 0 and (arbitrage_latest_pull_result is not defined or arbitrage_latest_pull_result.rc != 0) %}
              - arbitrage image: {{ arbitrage_pull_result.stderr | default('Unknown error') }}
              {% endif %}
              {% if redis_pull_result.rc != 0 %}
              - redis image: {{ redis_pull_result.stderr | default('Unknown error') }}
              {% endif %}
          when: (arbitrage_pull_result.rc != 0 and (arbitrage_latest_pull_result is not defined or arbitrage_latest_pull_result.rc != 0)) or redis_pull_result.rc != 0

      rescue:
        - name: Handle Docker prefetch errors
          ansible.builtin.debug:
            msg: |
              WARNING: Failed to prefetch Docker images on destination server.
              This is not critical - images will be pulled when the bot starts.
              However, this may cause delays during startup.

  tasks:
    # ==========================================
    # PRE-MIGRATION: SOURCE MACHINE PREPARATION
    # ==========================================
    - name: Pre-migration tasks on source machine
      block:
        - name: Check if source machine is accessible
          ansible.builtin.ping:
          delegate_to: "{{ source_host }}"

        - name: Check if destination machine is accessible
          ansible.builtin.ping:
          delegate_to: "{{ destination_host }}"

        - name: Pause trading on source machine with retries
          ansible.builtin.uri:
            url: "http://127.0.0.1:8008/strategies/pause"
            method: POST
            timeout: 10
          delegate_to: "{{ source_host }}"
          register: pause_result
          until: pause_result.status is defined and pause_result.status == 200
          retries: 2
          delay: 5
          failed_when: pause_result.status is defined and pause_result.status != 200

        - name: Display pause result
          ansible.builtin.debug:
            msg: "Trading pause result: {{ pause_result.status | default('Failed to connect') }}"

        - name: Wait for graceful trading shutdown
          ansible.builtin.pause:
            seconds: 5
            prompt: "Waiting for trading to pause gracefully..."

        - name: Stop Docker containers on source machine
          ansible.builtin.command:
            cmd: docker-compose -f docker-compose.yaml down
            chdir: "/home/<USER>"
          delegate_to: "{{ source_host }}"
          register: docker_stop_result
          failed_when: false

        - name: Display Docker stop result
          ansible.builtin.debug:
            msg: "Docker stop result: {{ docker_stop_result.rc }}"

        - name: Wait for Docker containers to fully stop
          ansible.builtin.pause:
            seconds: 10
            prompt: "Waiting for Docker containers to stop completely..."

      rescue:
        - name: Handle source machine preparation errors
          ansible.builtin.debug:
            msg: "Warning: Some pre-migration tasks failed. Check if source machine is accessible and services are running."

    # ==========================================
    # MIGRATION: FILE TRANSFER
    # ==========================================
    - name: File migration tasks
      block:
        - name: Check if files exist and gather metadata on source machine
          ansible.builtin.stat:
            path: "{{ item.src }}"
          delegate_to: "{{ source_host }}"
          register: source_files_check
          loop: "{{ files_to_migrate }}"

        - name: Gather metadata for directories on source machine
          ansible.builtin.stat:
            path: "{{ item.src | dirname }}"
          delegate_to: "{{ source_host }}"
          register: source_dirs_check
          loop: "{{ files_to_migrate }}"

        - name: Display missing files warning
          ansible.builtin.debug:
            msg: "Warning: File {{ item.item.src }} not found on source machine"
          loop: "{{ source_files_check.results }}"
          when: not item.stat.exists

        - name: Create backup of existing files on destination
          ansible.builtin.copy:
            src: "{{ item.dest }}"
            dest: "{{ item.dest }}.backup.{{ ansible_date_time.epoch }}"
            remote_src: yes
          delegate_to: "{{ destination_host }}"
          loop: "{{ files_to_migrate }}"
          when: item.backup | default(false)
          failed_when: false

        - name: Create temporary directory for file copies on source machine
          ansible.builtin.file:
            path: "/tmp/ansible-migration-{{ ansible_date_time.epoch }}"
            state: directory
            mode: '0755'
          delegate_to: "{{ source_host }}"

        - name: Create copies of files that might not be accessible to ansible_user
          ansible.builtin.copy:
            src: "{{ item.item.src }}"
            dest: "/tmp/ansible-migration-{{ ansible_date_time.epoch }}/{{ item.item.src | basename }}"
            remote_src: yes
            mode: '0644'
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
          delegate_to: "{{ source_host }}"
          become: yes
          register: copy_results
          loop: "{{ source_files_check.results }}"
          when: item.stat.exists
          failed_when: false

        - name: Display copy results
          ansible.builtin.debug:
            msg: "Copy result for {{ item.item.item.src }}: {{ 'Success' if item.changed else 'No change needed' }}"
          loop: "{{ copy_results.results }}"
          when: item is defined

        - name: Debug encryption key before encryption
          ansible.builtin.debug:
            msg: "Encryption key for {{ item.item.item.src | basename }}: {{ migration_encryption_key[:8] }}... (length: {{ migration_encryption_key | length }})"
          delegate_to: "{{ source_host }}"
          loop: "{{ copy_results.results }}"
          when: item is defined and item.item.item.encrypt | default(false)

        - name: Encrypt files on source machine that have encrypt flag set to true
          ansible.builtin.shell:
            cmd: >
              openssl enc -aes-256-cbc -pbkdf2 -iter 10000 -salt -md sha256 -in "{{ item.item.item.src | basename }}"
              -out "{{ item.item.item.src | basename }}.enc"
              -pass pass:"{{ migration_encryption_key }}" &&
              mv "{{ item.item.item.src | basename }}.enc" "{{ item.item.item.src | basename }}"
            chdir: "/tmp/ansible-migration-{{ ansible_date_time.epoch }}"
          delegate_to: "{{ source_host }}"
          loop: "{{ copy_results.results }}"
          when: item is defined and item.item.item.encrypt | default(false)
          register: encrypt_results
          failed_when: false

        - name: Display encryption results
          ansible.builtin.debug:
            msg: "Encryption result: {{ 'Success' if item.rc is defined and item.rc == 0 else 'Failed - Error code: ' ~ (item.rc | default('unknown')) }}"
          loop: "{{ encrypt_results.results }}"
          when: item is defined and item.skipped is not defined

        - name: Display encryption completion status
          ansible.builtin.debug:
            msg: "Encryption completed for {{ encrypt_results.results | selectattr('rc', 'defined') | selectattr('rc', 'eq', 0) | list | length }} files"

        - name: Fail if any encryption failed
          ansible.builtin.fail:
            msg: "Encryption failed for one or more files. Migration process stopped."
          when: encrypt_results.results | selectattr('rc', 'defined') | selectattr('rc', 'ne', 0) | list | length > 0

        - name: Fetch files from source machine to local temp directory with metadata
          ansible.builtin.fetch:
            src: "/tmp/ansible-migration-{{ ansible_date_time.epoch }}/{{ item.item.src | basename }}"
            dest: "{{ migration_temp_dir }}/{{ inventory_hostname }}/{{ item.item.src | basename }}"
            flat: yes
          delegate_to: "{{ source_host }}"
          loop: "{{ source_files_check.results }}"
          when: item.stat.exists
          register: fetch_results

        - name: Ensure destination directories exist
          ansible.builtin.file:
            path: "{{ item.0.dest | dirname }}"
            state: directory
            owner: "{{ item.1.stat.pw_name }}"
            group: "{{ item.1.stat.gr_name }}"
            mode: "{{ item.1.stat.mode }}"
          delegate_to: "{{ destination_host }}"
          loop: "{{ files_to_migrate | zip(source_dirs_check.results) | list }}"
          when: item.0.dest | dirname != "~"
          become: yes


        - name: Transfer all files to destination machine preserving original ownership and permissions
          ansible.builtin.copy:
            src: "{{ migration_temp_dir }}/{{ inventory_hostname }}/{{ item.0.src | basename }}"
            dest: "{{ item.0.dest }}"
            owner: "{{ item.1.stat.pw_name }}"
            group: "{{ item.1.stat.gr_name }}"
            mode: "{{ item.1.stat.mode }}"
          delegate_to: "{{ destination_host }}"
          loop: "{{ files_to_migrate | zip(source_files_check.results) | list }}"
          when: item.1.stat.exists
          register: transfer_results
          become: yes

        - name: Verify encryption key is still available for decryption
          ansible.builtin.fail:
            msg: "Encryption key is not available for decryption. This should not happen."
          when: migration_encryption_key is not defined or migration_encryption_key == ""

        - name: Debug encryption key before decryption
          ansible.builtin.debug:
            msg: "Decryption key for {{ item.0.dest }}: {{ migration_encryption_key[:8] }}... (length: {{ migration_encryption_key | length }})"
          delegate_to: "{{ destination_host }}"
          loop: "{{ files_to_migrate | zip(source_files_check.results) | list }}"
          when: item.1.stat.exists and item.0.encrypt | default(false)

        - name: Verify encrypted file exists before decryption
          ansible.builtin.stat:
            path: "{{ item.0.dest }}"
          delegate_to: "{{ destination_host }}"
          loop: "{{ files_to_migrate | zip(source_files_check.results) | list }}"
          when: item.1.stat.exists and item.0.encrypt | default(false)
          register: encrypted_file_check

        - name: Display encrypted file info
          ansible.builtin.debug:
            msg: "Encrypted file exists: {{ item.stat.exists }}, size: {{ item.stat.size | default('unknown') }} bytes"
          loop: "{{ encrypted_file_check.results }}"
          when: item is defined and item.skipped is not defined

        - name: Decrypt encrypted files on destination machine in their final location
          ansible.builtin.shell:
            cmd: >
              openssl enc -aes-256-cbc -d -pbkdf2 -iter 10000 -salt -md sha256 -in "{{ item.0.dest }}"
              -out "{{ item.0.dest }}.dec"
              -pass pass:"{{ migration_encryption_key }}" &&
              mv "{{ item.0.dest }}.dec" "{{ item.0.dest }}"
          delegate_to: "{{ destination_host }}"
          loop: "{{ files_to_migrate | zip(source_files_check.results) | list }}"
          when: item.1.stat.exists and item.0.encrypt | default(false)
          register: decrypt_results
          failed_when: false
          become: yes

        - name: Restore file ownership and permissions after decryption
          ansible.builtin.file:
            path: "{{ item.0.dest }}"
            owner: "{{ item.1.stat.pw_name }}"
            group: "{{ item.1.stat.gr_name }}"
            mode: "{{ item.1.stat.mode }}"
          delegate_to: "{{ destination_host }}"
          loop: "{{ files_to_migrate | zip(source_files_check.results) | list }}"
          when: item.1.stat.exists and item.0.encrypt | default(false)
          become: yes
          register: restore_permissions_results

        - name: Verify file ownership and permissions after decryption
          ansible.builtin.stat:
            path: "{{ item.0.dest }}"
          delegate_to: "{{ destination_host }}"
          loop: "{{ files_to_migrate | zip(source_files_check.results) | list }}"
          when: item.1.stat.exists and item.0.encrypt | default(false)
          register: post_decrypt_file_check

        - name: Display file ownership verification after decryption
          ansible.builtin.debug:
            msg: |
              File ownership verification for {{ item.item.0.dest }}:
              - Owner: {{ item.stat.pw_name }} (expected: {{ item.item.1.stat.pw_name }})
              - Group: {{ item.stat.gr_name }} (expected: {{ item.item.1.stat.gr_name }})
              - Mode: {{ item.stat.mode }} (expected: {{ item.item.1.stat.mode }})
              - Status: {{ 'OK' if (item.stat.pw_name == item.item.1.stat.pw_name and item.stat.gr_name == item.item.1.stat.gr_name and item.stat.mode == item.item.1.stat.mode) else 'MISMATCH' }}
          loop: "{{ post_decrypt_file_check.results }}"
          when: item is defined and item.skipped is not defined

        - name: Warn about ownership/permission mismatches
          ansible.builtin.debug:
            msg: |
              WARNING: File ownership/permissions mismatch detected for {{ item.item.0.dest }}!
              This may cause issues with file access. Please verify manually.
          loop: "{{ post_decrypt_file_check.results }}"
          when: item is defined and item.skipped is not defined and (item.stat.pw_name != item.item.1.stat.pw_name or item.stat.gr_name != item.item.1.stat.gr_name or item.stat.mode != item.item.1.stat.mode)

        - name: Display decryption results with detailed error information
          ansible.builtin.debug:
            msg: |
              Decryption result:
              - Return code: {{ item.rc | default('unknown') }}
              - Status: {{ 'Success' if item.rc is defined and item.rc == 0 else 'Failed' }}
              - Error output: {{ item.stderr | default('No error output') }}
              - Standard output: {{ item.stdout | default('No output') }}
          loop: "{{ decrypt_results.results }}"
          when: item is defined and item.skipped is not defined

        - name: Display detailed error for failed decryptions
          ansible.builtin.debug:
            msg: |
              DECRYPTION FAILED:
              - Command: openssl enc -aes-256-cbc -d -pbkdf2 -iter 10000 -salt -md sha256
              - Error: {{ item.stderr | default('No error details') }}
              - Return code: {{ item.rc }}
              - This usually indicates:
                1. Wrong encryption key
                2. Corrupted encrypted file
                3. File was not properly encrypted
          loop: "{{ decrypt_results.results }}"
          when: item is defined and item.skipped is not defined and item.rc is defined and item.rc != 0

        - name: Fail if any decryption failed
          ansible.builtin.fail:
            msg: |
              Decryption failed for one or more files. Migration process stopped.
              Check the detailed error messages above for troubleshooting.
              Common causes:
              1. Encryption key mismatch between source and destination
              2. Corrupted file during transfer
              3. OpenSSL version differences
          when: decrypt_results.results | selectattr('rc', 'defined') | selectattr('rc', 'ne', 0) | list | length > 0

      rescue:
        - name: Handle file migration errors
          ansible.builtin.fail:
            msg: "Critical error during file migration. Please check manually."

    # ==========================================
    # POST-MIGRATION: DESTINATION MACHINE SETUP
    # ==========================================
    - name: Post-migration tasks on destination machine
      block:
        - name: Check if destination machine is accessible
          ansible.builtin.ping:
          delegate_to: "{{ destination_host }}"

        - name: Verify restart script exists on destination
          ansible.builtin.stat:
            path: "~/restart.sh"
          delegate_to: "{{ destination_host }}"
          register: restart_script_check

        - name: Fail if restart script is missing
          ansible.builtin.fail:
            msg: "Restart script ~/restart.sh not found on destination machine"
          when: not restart_script_check.stat.exists

        - name: Start bot on destination machine
          ansible.builtin.command:
            cmd: ./restart.sh
            chdir: "/home/<USER>"
          delegate_to: "{{ destination_host }}"
          register: restart_result

        - name: Display restart result
          ansible.builtin.debug:
            msg: "Bot restart result: {{ restart_result.rc }}"

        - name: Wait for services to start
          ansible.builtin.pause:
            seconds: 30
            prompt: "Waiting for services to start on destination machine..."

        - name: Verify bot is running on destination
          ansible.builtin.uri:
            url: "http://127.0.0.1:8008/health"
            method: GET
            timeout: 10
          delegate_to: "{{ destination_host }}"
          register: health_check
          failed_when: false

        - name: Display health check result
          ansible.builtin.debug:
            msg: "Bot health check: {{ health_check.status | default('Service not responding') }}"

        - name: Verify health check response
          ansible.builtin.fail:
            msg: "Bot health check failed. Status: {{ health_check.status | default('Service not responding') }}, Body: {{ health_check.json | default('No response') }}"
          when: health_check.status is not defined or health_check.status != 200 or health_check.json is not defined or health_check.json.status != 'UP'

      rescue:
        - name: Handle post-migration errors
          ansible.builtin.debug:
            msg: "Warning: Some post-migration tasks failed. Please verify manually."

  post_tasks:
    - name: Migration summary
      ansible.builtin.debug:
        msg: |
          Migration Summary for {{ inventory_hostname }}:
          - Source: {{ source_host }}
          - Destination: {{ destination_host }}
          - Files migrated: {{ files_to_migrate | length }}
          - Status: {% if transfer_results is succeeded %}SUCCESS{% else %}PARTIAL/FAILED{% endif %}

          Next steps:
          1. Verify bot is running on destination: {{ destination_host }}
          2. Check logs for any issues
          3. If successful, you can safely decommission source: {{ source_host }}

    - name: Cleanup temporary directory on source machine
      ansible.builtin.file:
        path: "/tmp/ansible-migration-{{ ansible_date_time.epoch }}"
        state: absent
      delegate_to: "{{ source_host }}"
      failed_when: false


    - name: Cleanup temporary files on control node
      ansible.builtin.file:
        path: "{{ migration_temp_dir }}"
        state: absent
      delegate_to: localhost
      run_once: true
